#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 Testovanie eSpo<PERSON>nka Tina CMS setup...\n');

// Test 1: Kontrola základných súborov
console.log('1. Kontrola základných súborov...');
const requiredFiles = [
  'package.json',
  'next.config.js',
  'tsconfig.json',
  'tina/config.ts',
  'pages/index.tsx',
  'pages/blog/index.tsx',
  'pages/blog/[slug].tsx',
  'styles/globals.css',
  'README.md'
];

let missingFiles = [];
requiredFiles.forEach(file => {
  if (!fs.existsSync(path.join(__dirname, '..', file))) {
    missingFiles.push(file);
  }
});

if (missingFiles.length === 0) {
  console.log('✅ Všetky základné súbory sú prítomne');
} else {
  console.log('❌ Chýbajúce súbory:', missingFiles.join(', '));
}

// Test 2: Kontrola content štruktúry
console.log('\n2. Kontrola content štruktúry...');
const contentDirs = [
  'content/posts',
  'content/categories',
  'content/pages',
  'content/settings',
  'public/assets'
];

let missingDirs = [];
contentDirs.forEach(dir => {
  if (!fs.existsSync(path.join(__dirname, '..', dir))) {
    missingDirs.push(dir);
  }
});

if (missingDirs.length === 0) {
  console.log('✅ Content štruktúra je správna');
} else {
  console.log('❌ Chýbajúce priečinky:', missingDirs.join(', '));
}

// Test 3: Kontrola blog postov
console.log('\n3. Kontrola blog postov...');
const postsDir = path.join(__dirname, '..', 'content/posts');
if (fs.existsSync(postsDir)) {
  const posts = fs.readdirSync(postsDir).filter(file => file.endsWith('.md'));
  console.log(`✅ Nájdených ${posts.length} blog postov:`, posts.join(', '));
} else {
  console.log('❌ Priečinok content/posts neexistuje');
}

// Test 4: Kontrola kategórií
console.log('\n4. Kontrola kategórií...');
const categoriesDir = path.join(__dirname, '..', 'content/categories');
if (fs.existsSync(categoriesDir)) {
  const categories = fs.readdirSync(categoriesDir).filter(file => file.endsWith('.md'));
  console.log(`✅ Nájdených ${categories.length} kategórií:`, categories.join(', '));
} else {
  console.log('❌ Priečinok content/categories neexistuje');
}

// Test 5: Kontrola package.json závislostí
console.log('\n5. Kontrola závislostí...');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
const requiredDeps = ['next', 'react', 'react-dom', 'tinacms', '@tinacms/cli'];
const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);

if (missingDeps.length === 0) {
  console.log('✅ Všetky potrebné závislosti sú prítomné');
} else {
  console.log('❌ Chýbajúce závislosti:', missingDeps.join(', '));
}

// Test 6: Kontrola environment súborov
console.log('\n6. Kontrola environment súborov...');
if (fs.existsSync(path.join(__dirname, '..', '.env.example'))) {
  console.log('✅ .env.example súbor existuje');
} else {
  console.log('❌ .env.example súbor chýba');
}

console.log('\n🎉 Test dokončený!');
console.log('\n📋 Ďalšie kroky:');
console.log('1. Spustite: npm install');
console.log('2. Vytvorte .env.local súbor s Tina credentials');
console.log('3. Spustite: npm run dev');
console.log('4. Otvorte http://localhost:3000');
console.log('5. Otvorte http://localhost:3000/admin pre Tina CMS');
