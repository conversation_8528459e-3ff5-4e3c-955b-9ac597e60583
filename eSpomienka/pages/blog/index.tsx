import { GetStaticProps } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { getPosts, getCategories } from '../../lib/tina'

interface Post {
  id: string
  title: string
  slug: string
  excerpt: string
  date: string
  image?: string
  readTime: number
  category: {
    name: string
    slug: string
    color: string
  }
}

interface Category {
  name: string
  slug: string
  color: string
}

interface BlogPageProps {
  posts: Post[]
  categories: Category[]
}

export default function BlogPage({ posts, categories }: BlogPageProps) {
  return (
    <>
      <Head>
        <title>Blog - eSpomienka</title>
        <meta name="description" content="Rady, inšpirácie a technológie pre digitálne spomienky" />
      </Head>

      <div className="min-h-screen bg-cream">
        {/* Header */}
        <header className="bg-primary shadow-sm">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-3">
                <span className="font-playfair text-2xl font-semibold text-white">eSpomienka</span>
              </Link>
              <nav className="flex items-center space-x-8">
                <Link href="/" className="text-white hover:text-gold transition-colors">
                  Domov
                </Link>
                <Link href="/blog" className="text-gold font-medium">
                  Blog
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-r from-primary to-primary-dark">
          <div className="container mx-auto px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-4">
              Rady a inšpirácie
            </h1>
            <p className="text-xl text-gray-200 max-w-2xl mx-auto">
              Pomáhame vám vytvoriť dokonalé spomienky na vašich milovaných
            </p>
          </div>
        </section>

        {/* Categories */}
        <section className="py-8 bg-white">
          <div className="container mx-auto px-6">
            <div className="flex flex-wrap gap-4 justify-center">
              {categories.map((category) => (
                <Link
                  key={category.slug}
                  href={`/blog/category/${category.slug}`}
                  className="px-4 py-2 rounded-full text-white font-medium transition-transform hover:scale-105"
                  style={{ backgroundColor: category.color }}
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* Posts Grid */}
        <section className="py-16">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  {post.image && (
                    <div className="h-48 bg-gray-200">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span
                        className="px-3 py-1 rounded-full text-white text-sm font-medium"
                        style={{ backgroundColor: post.category.color }}
                      >
                        {post.category.name}
                      </span>
                      <span className="text-gray-500 text-sm">{post.readTime} min čítania</span>
                    </div>
                    <h2 className="text-xl font-playfair font-semibold text-gray-800 mb-3 line-clamp-2">
                      {post.title}
                    </h2>
                    <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-sm">
                        {new Date(post.date).toLocaleDateString('sk-SK')}
                      </span>
                      <Link
                        href={`/blog/${post.slug}`}
                        className="text-gold hover:text-gold-dark font-medium transition-colors"
                      >
                        Čítať viac →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  )
}

export const getStaticProps: GetStaticProps = async () => {
  const posts = await getPosts()
  const categories = await getCategories()

  return {
    props: {
      posts: posts.filter(post => post?.published),
      categories,
    },
    revalidate: 60,
  }
}
