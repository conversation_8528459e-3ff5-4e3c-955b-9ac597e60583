import { GetStaticProps, GetStaticPaths } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { getPosts, getPost } from '../../lib/tina'
import { TinaMarkdown } from 'tinacms/dist/rich-text'

interface Post {
  id: string
  title: string
  slug: string
  excerpt: string
  date: string
  image?: string
  readTime: number
  body: any
  category: {
    name: string
    slug: string
    color: string
  }
}

interface PostPageProps {
  post: Post
}

export default function PostPage({ post }: PostPageProps) {
  return (
    <>
      <Head>
        <title>{post.title} - eSpomienka Blog</title>
        <meta name="description" content={post.excerpt} />
        <meta property="og:title" content={post.title} />
        <meta property="og:description" content={post.excerpt} />
        {post.image && <meta property="og:image" content={post.image} />}
      </Head>

      <div className="min-h-screen bg-cream">
        {/* Header */}
        <header className="bg-primary shadow-sm">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-3">
                <span className="font-playfair text-2xl font-semibold text-white">eSpomienka</span>
              </Link>
              <nav className="flex items-center space-x-8">
                <Link href="/" className="text-white hover:text-gold transition-colors">
                  Domov
                </Link>
                <Link href="/blog" className="text-white hover:text-gold transition-colors">
                  Blog
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Article */}
        <article className="py-16">
          <div className="container mx-auto px-6 max-w-4xl">
            {/* Breadcrumb */}
            <nav className="mb-8">
              <Link href="/blog" className="text-gold hover:text-gold-dark">
                ← Späť na blog
              </Link>
            </nav>

            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <span
                  className="px-3 py-1 rounded-full text-white text-sm font-medium"
                  style={{ backgroundColor: post.category.color }}
                >
                  {post.category.name}
                </span>
                <span className="text-gray-500 text-sm">{post.readTime} min čítania</span>
                <span className="text-gray-500 text-sm">
                  {new Date(post.date).toLocaleDateString('sk-SK')}
                </span>
              </div>
              <h1 className="text-4xl md:text-5xl font-playfair font-bold text-gray-800 mb-4">
                {post.title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">{post.excerpt}</p>
            </header>

            {/* Featured Image */}
            {post.image && (
              <div className="mb-8">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            )}

            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <TinaMarkdown content={post.body} />
            </div>

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <Link href="/blog" className="text-gold hover:text-gold-dark font-medium">
                  ← Späť na blog
                </Link>
                <div className="flex items-center space-x-4">
                  <span className="text-gray-500">Zdieľať:</span>
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Facebook
                  </a>
                </div>
              </div>
            </footer>
          </div>
        </article>
      </div>
    </>
  )
}

export const getStaticPaths: GetStaticPaths = async () => {
  const posts = await getPosts()
  const paths = posts
    .filter(post => post?.published && post?.slug)
    .map(post => ({
      params: { slug: post!.slug }
    }))

  return {
    paths,
    fallback: false,
  }
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const slug = params?.slug as string
  const post = await getPost(slug)

  if (!post) {
    return {
      notFound: true,
    }
  }

  return {
    props: {
      post,
    },
    revalidate: 60,
  }
}
