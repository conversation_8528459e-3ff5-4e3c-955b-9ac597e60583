# eSpomienka - <PERSON><PERSON><PERSON><PERSON>e spomienky s <PERSON>

Moderná webstránka pre eSpomienka s integr<PERSON><PERSON><PERSON> Tina CMS pre správu obsahu.

## 🚀 Funkcie

- **Next.js** - Moderný React framework pre statické generovanie
- **<PERSON>MS** - Git-based headless CMS pre správu obsahu
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Responsive Design** - Optimalizované pre všetky zariadenia

## 📁 Štruktúra projektu

```
eSpomienka/
├── content/              # Tina CMS obsah
│   ├── posts/           # Blog posty (Markdown)
│   ├── categories/      # Kategórie (Markdown)
│   ├── pages/           # Statické stránky
│   └── settings/        # Nastavenia webu
├── pages/               # Next.js str<PERSON>ky
│   ├── blog/           # Blog stránky
│   └── index.tsx       # Hlavná stránka
├── components/          # React komponenty
├── lib/                # Utility funkcie
├── styles/             # CSS štýly
├── tina/               # Tina CMS konfigurácia
└── public/             # Statické súbory
```

## 🛠️ Inštalácia a spustenie

### 1. Inštalácia závislostí

```bash
npm install
```

### 2. Konfigurácia environment variables

Skopírujte `.env.example` do `.env.local`:

```bash
cp .env.example .env.local
```

Vyplňte potrebné hodnoty:

```env
NEXT_PUBLIC_TINA_CLIENT_ID=your_tina_client_id_here
TINA_TOKEN=your_tina_token_here
HEAD=main
```

### 3. Spustenie development servera

```bash
npm run dev
```

Webstránka bude dostupná na `http://localhost:3000`
Tina CMS admin rozhranie na `http://localhost:3000/admin`

## 📝 Správa obsahu

### Blog posty

1. Otvorte admin rozhranie na `/admin`
2. Prihláste sa pomocí GitHub účtu
3. Vytvorte nový post v sekcii "Blog Posts"
4. Vyplňte všetky potrebné polia
5. Uložte a publikujte

### Kategórie

Kategórie sa spravujú v sekcii "Categories" v admin rozhraní.

### Nastavenia

Globálne nastavenia webu sa nachádzajú v sekcii "Settings".

## 🚀 Deployment na Tina.io

### 1. Príprava repository

```bash
git add .
git commit -m "Initial Tina CMS setup"
git push origin main
```

### 2. Konfigurácia na Tina.io

1. Prihláste sa na [tina.io](https://tina.io)
2. Vytvorte nový projekt
3. Pripojte váš GitHub repository
4. Nastavte build command: `npm run build`
5. Nastavte output directory: `out`
6. Pridajte environment variables

### 3. Environment variables na Tina.io

```
NEXT_PUBLIC_TINA_CLIENT_ID=your_client_id
TINA_TOKEN=your_token
HEAD=main
```

### 4. Build commands

```bash
# Development
npm run dev

# Production build
npm run build

# Export static files
npm run export
```

## 📱 Funkcie

### Blog systém
- Markdown editor s rich text
- Kategorizácia postov
- SEO optimalizácia
- Responsive design

### Content Management
- Git-based workflow
- Visual editing
- Media management
- Preview mode

### Performance
- Static site generation
- Image optimization
- Code splitting
- Fast loading times

## 🔧 Customizácia

### Pridanie novej stránky

1. Vytvorte súbor v `pages/`
2. Pridajte do Tina schémy ak potrebuje CMS
3. Aktualizujte navigáciu

### Úprava dizajnu

Štýly sa nachádzajú v:
- `styles/globals.css` - Globálne štýly
- Tailwind classes v komponentoch
- Custom CSS variables

### Rozšírenie Tina schémy

Upravte `tina/config.ts` pre pridanie nových polí alebo kolekcií.

## 📞 Podpora

Pre technickú podporu kontaktujte:
- Email: <EMAIL>
- Telefón: +421 951 553 464

## 📄 Licencia

© 2025 eSpomienka. Všetky práva vyhradené.
