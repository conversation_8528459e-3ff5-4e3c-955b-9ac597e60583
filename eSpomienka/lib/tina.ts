import { client } from '../tina/__generated__/client'

export const getPosts = async () => {
  const postsResponse = await client.queries.postConnection()
  return postsResponse.data.postConnection.edges?.map(edge => edge?.node) || []
}

export const getPost = async (slug: string) => {
  const posts = await getPosts()
  return posts.find(post => post?.slug === slug)
}

export const getCategories = async () => {
  const categoriesResponse = await client.queries.categoryConnection()
  return categoriesResponse.data.categoryConnection.edges?.map(edge => edge?.node) || []
}

export const getPostsByCategory = async (categorySlug: string) => {
  const posts = await getPosts()
  return posts.filter(post => 
    post?.category && 
    typeof post.category === 'object' && 
    'slug' in post.category && 
    post.category.slug === categorySlug
  )
}

export const getSettings = async () => {
  try {
    const settingsResponse = await client.queries.settings({ relativePath: 'site.json' })
    return settingsResponse.data.settings
  } catch (error) {
    console.error('Error fetching settings:', error)
    return null
  }
}
