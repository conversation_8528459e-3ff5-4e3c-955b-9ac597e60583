@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap');

:root {
  --color-primary: #2C3E50;
  --color-primary-dark: #1A252F;
  --color-gold: #DAA520;
  --color-gold-dark: #B8860B;
  --color-cream: #FAFAFA;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
}

.font-playfair {
  font-family: 'Playfair Display', serif;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-primary-dark {
  background-color: var(--color-primary-dark);
}

.bg-gold {
  background-color: var(--color-gold);
}

.bg-gold-dark {
  background-color: var(--color-gold-dark);
}

.bg-cream {
  background-color: var(--color-cream);
}

.text-primary {
  color: var(--color-primary);
}

.text-gold {
  color: var(--color-gold);
}

.text-gold-dark {
  color: var(--color-gold-dark);
}

.hover\:bg-gold-dark:hover {
  background-color: var(--color-gold-dark);
}

.hover\:text-gold:hover {
  color: var(--color-gold);
}

.hover\:text-gold-dark:hover {
  color: var(--color-gold-dark);
}

/* Prose styles for blog content */
.prose {
  color: #374151;
  max-width: none;
}

.prose h1 {
  color: #1f2937;
  font-weight: 800;
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose h2 {
  color: #1f2937;
  font-weight: 700;
  font-size: 1.5rem;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose h3 {
  color: #1f2937;
  font-weight: 600;
  font-size: 1.25rem;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  line-height: 1.75;
}

.prose strong {
  color: #1f2937;
  font-weight: 600;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose blockquote {
  font-weight: 500;
  font-style: italic;
  color: #1f2937;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-left: 1em;
}

/* Line clamp utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
